/*
 * Created Date: Friday, 1st August 2025, 16:49:06
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Monday, 4th August 2025 12:51:53
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../constants/constant.dart';
import 'ai_market_controller.dart';

/// one instance for this page
final AIMarketController _aiMarketController = AIMarketController();

class AIMarketMainPage extends StatelessWidget {
  const AIMarketMainPage({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) {
        _aiMarketController.onBack();
      },
      child: ColoredBox(
        color: GPColor.bgPrimary,
        child: Safe<PERSON><PERSON>(
          child: WebViewWidget(
            controller: _aiMarketController.webviewController
              ..setJavaScriptMode(JavaScriptMode.unrestricted)
              ..setNavigationDelegate(
                NavigationDelegate(
                  onProgress: (int progress) {},
                  onPageStarted: (String url) {},
                  onPageFinished: (String url) async {
                    logDebug('DEBUG: before inject token to url: $url');
                  },
                  onHttpError: _aiMarketController.onHttpError,
                  onWebResourceError: (error) {},
                  onNavigationRequest: (NavigationRequest request) {
                    logDebug('DEBUG: onHttpAuthRequest: ${request.url}');
                    return NavigationDecision.navigate;
                  },
                  onUrlChange: _aiMarketController.onUrlChange,
                  onHttpAuthRequest: (request) {
                    logDebug('DEBUG: onHttpAuthRequest: ${request.host}');
                  },
                ),
              )
              ..addJavaScriptChannel(
                GPWebConstants.kWebChannel,
                onMessageReceived: _aiMarketController.onMessageReceived,
              )
              ..setOnConsoleMessage(
                _aiMarketController.onConsoleMessage,
              )
              ..loadRequest(
                Uri.parse(
                  _aiMarketController.getWebUrl(),
                ),
              ),
          ),
        ),
      ),
    );
  }
}
