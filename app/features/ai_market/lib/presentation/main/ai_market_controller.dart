import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gp_core/configs/constants.dart';
import 'package:gp_core/configs/token_manager.dart';
import 'package:gp_core/utils/gp_sentry.dart';
import 'package:gp_core/utils/log.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../constants/web_constant.dart';

final class AIMarketController {
  final webviewController = WebViewController();

  final rxCanGoBack = ValueNotifier(false);

  String getWebUrl() {
    if (Constants.isStaging) {
      return GPWebConstants.kWebviewStagingUrl
          .replaceAll('{workspace_id}', Constants.workspaceId());
    }

    return GPWebConstants.kWebviewUrl
        .replaceAll('{workspace_id}', Constants.workspaceId());

    // return "http://localhost:8000/";
  }

  String getOSSUrl() {
    return '${Constants.appDomain}/project-management';
  }

  Future onBack() async {
    String jsCodeBackEvent = '''
      window.postMessage({
        type: "${GPWebConstants.kWebPostMessageBackEvent}" });
    ''';

    jsCodeBackEvent = jsCodeBackEvent.trim();

    logDebug('DEBUG: onBack: $jsCodeBackEvent');

    await webviewController.runJavaScript(jsCodeBackEvent).catchError((error) {
      logDebug('DEBUG: onBack error: $error');

      GPCoreTracker().appendMessage(
        'Flutter:aiMarket.onBack error: $error',
      );
    });

    logDebug('DEBUG: onBack DONE');
  }

  Future injectTokenToWeb() async {
    final token = await TokenManager.accessToken();

    final escapedToken = token.replaceAll("'", "\\'").replaceAll('"', '\\"');

    String jsCodeSetToken = '''
      window.postMessage({
        type: "${GPWebConstants.kWebPostMessageFlutterSyncAuthRequest}",
        token: "$escapedToken",
        workspaceId: "${Constants.workspaceId()}" });
    ''';

    jsCodeSetToken = jsCodeSetToken.trim();

    logDebug('DEBUG: injectTokenToWeb: $jsCodeSetToken');

    await webviewController.runJavaScript(jsCodeSetToken).catchError((error) {
      logDebug('DEBUG: injectTokenToWeb error: $error');

      GPCoreTracker().appendMessage(
        'Flutter:aiMarket.injectTokenToWeb error: $error',
      );
    });

    logDebug('DEBUG: injectTokenToWeb DONE');
  }

  Future changeWebLanguage() async {
    final languageCode = Constants.language();

    String jsCodeChangeLanguage = '''
      window.postMessage({
        type: "${GPWebConstants.kWebPostMessageLanguageChangedRequest}",
        language: "$languageCode" });
    ''';

    jsCodeChangeLanguage = jsCodeChangeLanguage.trim();

    webviewController.runJavaScript(jsCodeChangeLanguage).catchError((error) {
      logDebug('DEBUG: changeWebLanguage error: $error');

      GPCoreTracker().appendMessage(
        'Flutter:aiMarket.changeWebLanguage error: $error',
      );
    });

    logDebug('DEBUG: changeWebLanguage DONE');
  }

  Future setOriginResponse() async {
    final ossUrl = getOSSUrl().trim();

    String jsCodeOriginResponse = '''
      window.postMessage({
        type: "${GPWebConstants.kWebPostMessageOriginResponse}",
        token: "$ossUrl" });
    ''';

    jsCodeOriginResponse = jsCodeOriginResponse.trim();

    webviewController.runJavaScript(jsCodeOriginResponse).catchError((error) {
      logDebug('DEBUG: setOriginResponse error: $error');

      GPCoreTracker().appendMessage(
        'Flutter:aiMarket.setOriginResponse error: $error',
      );
    });

    logDebug('DEBUG: setOriginResponse DONE');
  }

  void onUrlChange(UrlChange urlChange) async {
    logDebug('DEBUG: onUrlChange: ${urlChange.url}');
    rxCanGoBack.value = await webviewController.canGoBack();
  }

  void onMessageReceived(JavaScriptMessage message) {
    final mess = message.message;
    logDebug('DEBUG: onMessageReceived: $mess');

    final messData = mess.convertJSMessageToMap();
    final messType = messData['type'];

    logDebug('DEBUG: onMessageReceived: messType $messType');
    if (messType.toString().toLowerCase() ==
        GPWebConstants.kCloseEvent.toLowerCase()) {
      SystemNavigator.pop(animated: true);
      return;
    } else if (messType == GPWebConstants.kOriginRequest) {
      GPCoreTracker().appendMessage(
        'Flutter:aiMarket.onMessageReceived: ${GPWebConstants.kOriginRequest}',
      );

      final isNeedSyncAuth = messData['isNeedSyncAuth'];

      if (isNeedSyncAuth) {
        injectTokenToWeb().then((v) async {
          await changeWebLanguage();
          await setOriginResponse();
        });
      }
    } else if (messType == GPWebConstants.kPathChangedRequest) {
      GPCoreTracker().appendMessage(
        'Flutter:aiMarket.onMessageReceived: Token handled successfully!',
      );
    }
  }

  void onHttpError(HttpResponseError error) {
    logDebug('DEBUG: onHttpError: ${error.request?.uri.host}');

    GPCoreTracker().appendError(
      'Flutter:aiMarket.onHttpError:error',
      data: {
        'error request': error.request,
        'error response': error.response,
      },
    );
  }

  void onConsoleMessage(JavaScriptConsoleMessage message) {
    final mess = message.message;
    logDebug('DEBUG: console log from web: $mess');

    GPCoreTracker().appendMessage(
      'Flutter:aiMarket.onConsoleMessage: $mess',
    );
  }
}

extension _StringConvertExt on String {
  Map<String, dynamic> convertJSMessageToMap() {
    try {
      String replacedStr =
          replaceAllMapped(RegExp(r'(\w+)\s*:'), (m) => '"${m[1]}":')
              // wrap value không phải boolean/null/number/string bằng dấu nháy
              .replaceAllMapped(RegExp(r':\s*([^,"\{\}\[\]\s][^,\}\s]*)'), (m) {
        final val = m[1]!;
        // Nếu là boolean, null, hoặc số thì giữ nguyên
        if (val == 'true' ||
            val == 'false' ||
            val == 'null' ||
            num.tryParse(val) != null) {
          return ': $val';
        }
        return ': "$val"';
      });
      logDebug('DEBUG: convertJSMessageToMap replacedStr: $replacedStr');

      return jsonDecode(replacedStr);
    } catch (ex, s) {
      GPCoreTracker().appendError(
        'Flutter:aiMarket.convertJSMessageToMap:error',
        data: {'error': ex, 'stacktrace': s},
      );
    }

    return {};
  }
}
