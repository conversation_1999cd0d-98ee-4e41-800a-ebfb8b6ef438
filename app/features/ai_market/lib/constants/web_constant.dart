final class GPWebConstants {
  static const String kWebviewTestUrl =
      'http://192.168.10.218:8000/Downloads/test_webview/index.html';

  static const String kWebviewUrl = 'https://staging-aimarket.gapowork.vn';

  static const String kWebviewStagingUrl =
      'https://staging-aimarket.gapowork.vn';

  static const String kWebPostMessageFlutterSyncAuthRequest =
      'SYNC_AUTH_REQUEST';
  static const String kWebPostMessageLanguageChangedRequest =
      'LANGUAGE_CHANGED_REQUEST';
  static const String kWebPostMessageOriginResponse = 'ORIGIN_RESPONSE';

  static const String kWebChannel = 'GPWebChannel';

  /// Web: Khi nhận ORIGIN_REQUEST:
  /// Host gửi lại 3 message:
  /// ORIGIN_RESPONSE: gửi origin để iframe lưu
  /// LANGUAGE_CHANGED_REQUEST: gửi ngôn ngữ hiện tại
  static const String kOriginRequest = 'ORIGIN_REQUEST';
  static const String kPathChangedRequest = 'PATHNAME_CHANGED_REQUEST';
  static const String kCloseEvent = 'CLOSE_EVENT';
  static const String kWebPostMessageBackEvent = 'BACK_EVENT';

  /// Web: Communication channel GPWebChannel not found.
  static const String kWebChannelError = 'web_channel_not_found';

  /// Web: ReceiveToken was called but no token was provided.
  static const String kTokenError = 'web_token_error_no_token_provided';
}
