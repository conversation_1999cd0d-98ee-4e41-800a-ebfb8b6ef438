import 'package:gp_core/core.dart';
import 'package:gp_core/utils/dio_wrapper.dart';

import '../presentation/main/ai_market.main.page.dart';
import 'router_name.dart';

mixin AIMarketPages {
  static List<GetPage> pages = [
    GetPage(
      name: RouterName.routeWithoutAnimation(AIMarketRoutes.aiMarketMain),
      transitionDuration: Duration.zero,
      page: () => const DioLogWrapperWidget(child: AIMarketMainPage()),
    ),
    GetPage(
      name: AIMarketRoutes.aiMarketMain,
      page: () => const AIMarketMainPage(),
    ),
  ];
}
