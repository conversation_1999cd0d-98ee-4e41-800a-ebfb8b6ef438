# coin

Project Management - Quản lý công việc nâng cao

## Git flow
- develop: 
  - default branch.
  - Sử dụng branch này để:
    - merge toàn bộ code trong quá trình development.
    - build staging/production/uat.
- prod:
  - branch chứa code cho bản release hiện tại, đư<PERSON><PERSON> tạo ra nếu code khác `develop`, mặc định không có.
  - Khi tester verify, sẽ merge toàn bộ code ở `develop` qua `release`
  - <PERSON>hi publish app, sẽ đánh tag dựa trên version bản build. Quy tắc đánh tag (apply chung cho cả Gapo): x.y.z. Trong đó:
    - x: số tuổi đời của Gapo, tính từ 2020.
    - y: số tháng trong năm (1->12)
    - z: số bản build trong tháng: bản đầu tiên là 1, bản tiếp theo là 2....
    - e.g: 
      - 2.12.1
      - 2.12.2
      - 3.1.1
  - <PERSON>ử dụng branch này để:
    - merge toàn bộ code để publish app.
    - build release.
- prod-premise:
  - branch chứa code release của bản onPremise, đư<PERSON><PERSON> tạo ra nếu code khác `develop`, mặc định không có.
- release: *chỉ để push code cho team android*
  - Flutter team sẽ push code cho Android Team thông qua branch này.
    - Code trên branch này có thể lấy từ develop, prod, prod-premise tuỳ trường hợp.